CREATE TABLE IF NOT EXISTS `owned_vehicles` (
  `id` INT(11) NOT NULL AUTO_INCREMENT,
  `license` VARCHAR(50) DEFAULT NULL,
  `citizenid` VARCHAR(50) NOT NULL,
  `plate` VARCHAR(15) NOT NULL,
  `vehicle` LONGTEXT NOT NULL,
  `garage` VARCHAR(50) DEFAULT NULL,
  `state` INT(11) DEFAULT 0,
  `fuel` FLOAT DEFAULT 100,
  `engine` FLOAT DEFAULT 1000,
  `body` FLOAT DEFAULT 1000,
  `mods` LONGTEXT DEFAULT NULL,
  `drivingdistance` INT(11) DEFAULT 0,
  `status` VARCHAR(20) DEFAULT 'active',
  `depotprice` INT(11) DEFAULT 0,
  `paymentamount` INT(11) DEFAULT 0,
  `paymentsleft` INT(11) DEFAULT 0,
  `financetime` INT(11) DEFAULT 0,
  PRIMARY KEY (`id`),
  UNIQUE KEY `plate` (`plate`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;
