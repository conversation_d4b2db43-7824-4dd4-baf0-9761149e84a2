{"name": "resources", "version": "0.1.0", "homepage": "resources/build", "private": true, "dependencies": {"@testing-library/jest-dom": "^5.16.4", "@testing-library/react": "^13.3.0", "@testing-library/user-event": "^14.2.0", "@types/jest": "^27.5.1", "@types/node": "^17.0.36", "@types/react": "^18.0.9", "@types/react-dom": "^18.0.5", "animejs": "^3.2.1", "framer-motion": "^6.3.11", "react": "^18.0.0", "react-color": "^2.19.3", "react-dom": "^18.0.0", "react-scripts": "^5.0.1", "sass": "^1.52.1", "scss": "^0.2.4", "styled-components": "^5.3.5", "typescript": "^4.5.5", "web-vitals": "^2.1.3"}, "scripts": {"start": "cross-env PUBLIC_URL=/ craco start", "start:game": "cross-env IN_GAME_DEV=1 craco start", "build": "rimraf build && craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@craco/craco": "^6.4.3", "cross-env": "^7.0.3", "rimraf": "^3.0.2"}}