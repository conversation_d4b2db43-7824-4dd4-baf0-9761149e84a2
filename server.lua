Framework = nil

-- مقداردهی اولیه ESX
if Customize.Framework == 'ESX' then
    -- مقداردهی اولیه ESX با حلقه انتظار تا گرفتن شیء ESX
    while Framework == nil do
        TriggerEvent('esx:getSharedObject', function(obj) Framework = obj end)
        Citizen.Wait(10)
    end
end

Citizen.CreateThread(function()
    Citizen.Wait(2000)
    for _,v in pairs(GetPlayers()) do
        local Player
        if Customize.Framework == "ESX" then
            Player = Framework.GetPlayerFromId(tonumber(v))
        end
        if Player ~= nil then
            TriggerClientEvent('PlayerLoad', tonumber(v))
        end
    end
end)

RegisterNetEvent('esx:playerLoaded')
AddEventHandler('esx:playerLoaded', function(src)
    Wait(700)
    TriggerClientEvent('PlayerLoad', src)
end)

--state  = 1 ise garagede 0 da dışarıdadır 2 = Inpound

if Customize.Framework == 'ESX' then
    AddEventHandler('onResourceStart', function(resourceName)
        if (GetCurrentResourceName() ~= resourceName) then return end
        Wait(100)
        MySQL.update('UPDATE owned_vehicles SET state = 1 WHERE state = 0', {})
    end)

    Framework.RegisterServerCallback('GetVehicles', function(source, cb, job, playerjob)
        local Player = Framework.GetPlayerFromId(source)
        print('[DEBUG] GetVehicles called. job:', job, 'playerjob:', playerjob, 'Player.identifier:', Player and Player.identifier)
        if not Player then return cb(nil) end
        local query, params
        if job == 'Job' then
            query = 'SELECT * FROM owned_vehicles WHERE owner = ? AND job = ? AND state = 1'
            params = {Player.identifier, playerjob}
        elseif job == 'Impound' then
            query = 'SELECT * FROM owned_vehicles WHERE owner = ? AND state = 2'
            params = {Player.identifier}
        else
            query = 'SELECT * FROM owned_vehicles WHERE owner = ? AND state = 1'
            params = {Player.identifier}
        end
        MySQL.query(query, params, function(result)
            print('[DEBUG] SQL result:', json.encode(result))
            if result and result[1] then cb(result) else cb(nil) end
        end)
    end)

    RegisterNetEvent('SetVehState', function(state, plate, table, job, playerjob)
        print('[DEBUG] SetVehState called. state:', state, 'plate:', plate, 'table:', json.encode(table), 'job:', job, 'playerjob:', playerjob)
        if job == 'Job' then
            if state == 1 then
                MySQL.update('UPDATE owned_vehicles SET state = ?, fuel = ?, engine = ?, body = ?, job = ? WHERE plate = ?', {state, table.fuel, table.engine, table.body, playerjob, plate})
            else
                MySQL.update('UPDATE owned_vehicles SET state = ?, job = ? WHERE plate = ?', {state, playerjob, plate})
            end
        else
            if state == 1 then
                MySQL.update('UPDATE owned_vehicles SET state = ?, fuel = ?, engine = ?, body = ?, job = ? WHERE plate = ?', {state, table.fuel, table.engine, table.body, '', plate})
            else
                MySQL.update('UPDATE owned_vehicles SET state = ?, job = ? WHERE plate = ?', {state, '', plate})
            end
        end
    end)

    RegisterNetEvent('SetVehState0', function(state, plate)
        print('[DEBUG] SetVehState0 called. state:', state, 'plate:', plate)
        MySQL.update('UPDATE owned_vehicles SET state = ? WHERE plate = ?', {state, plate})
    end)

    Framework.RegisterServerCallback("IsVehOwned", function(source, cb, plate)
        local Player = Framework.GetPlayerFromId(source)
        if not Player then
            return cb(false)
        end

        MySQL.query('SELECT * FROM owned_vehicles WHERE plate = ? AND owner = ?', {plate, Player.identifier}, function(result)
            if result[1] then
                cb(true)
            else
                cb(false)
            end
        end)
    end)

    Framework.RegisterServerCallback("isPrice", function(source, cb, money)
        local Player = Framework.GetPlayerFromId(source)
        if not Player then return cb(false) end
        local playerMoney = Player.getMoney and Player.getMoney() or Player.money
        if playerMoney >= money then 
            if Player.removeMoney then
                Player.removeMoney(money)
            elseif Player.removeAccountMoney then
                Player.removeAccountMoney('money', money)
            end
            cb(true)
        else
            cb(false)
        end
    end)

    RegisterNetEvent('SetVehImpound', function(plate, body, engine, fuel)
        print('[DEBUG] SetVehImpound called. plate:', plate, 'body:', body, 'engine:', engine, 'fuel:', fuel)
        if IsVehicleOwned(plate) then
            MySQL.query('UPDATE owned_vehicles SET state = ?, body = ?, engine = ?, fuel = ? WHERE plate = ?',{2, body, engine, fuel, plate})
        end
    end)

    function IsVehicleOwned(plate)
        local result = MySQL.scalar.await('SELECT plate FROM owned_vehicles WHERE plate = ?', {plate})
        return result
    end
end

-- function ESC(type, data, table, callback) end