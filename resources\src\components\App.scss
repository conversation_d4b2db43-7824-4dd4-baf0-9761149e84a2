@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
/*
font-family: 'Inter', sans-serif;
*/

body {
  user-select: none;
  overflow: hidden;
//  background-color: rgb(0, 0, 0);
}

@media screen and (width: 2560px) and (height: 1440px) {
  html {
    font-size: 21px;
  }
}


// @media screen and (width: 1920px) and (height: 1080px) {
//   html {
//     font-size: 16px;
//   }
// }



@font-face {
  src: url('../Assets/fonts/Proxima_Nova_Condensed_Bold_Italic.otf') format("OpenType");
  font-family: 'Proxima_Nova_Condensed_Bold_Italic';
}
@font-face {
  src: url('../Assets/fonts/Proxima_Nova_Condensed_Regular.otf') format("OpenType");
  font-family: 'Proxima_Nova_Condensed_Regular';
}



.Main {
  position: absolute;
  height: 100%;
  width: 100%;
  top: 0;
  left: 0;
  overflow: hidden;
  .move {
    position: absolute;
    height: 100%;
    width: 80%;
    right: 0;
    top: 0;
//    background-color: #ff151525;
  }
}

@mixin Border() {
  position: absolute;
  width: 6.25rem;
  height: 1.5rem;
  top: 17.188rem;
  background: #00000080;
  border-bottom: 0rem solid #59acff00;
  z-index: 2;
  .Label {
    position: absolute;
    width: auto;
    height: auto;
    left: .5rem;
    top: .3rem;
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: .875rem;
    line-height: 1.063rem;
    text-transform: uppercase;
    color: #FFFFFF;
  }
  > p {
    position: absolute;
    width: auto;
    height: auto;
    right: .44rem;
    top: 0;
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: .875rem;
    line-height: 0;
    text-transform: uppercase;
    color: rgba(255, 255, 255, 0.5);
    > span {
      color: #FFFFFF;
    }
  }
}


.Label1 {
  position: absolute;
  width: auto;
  height: auto;
  left: 3.75rem;
  top: 7.938rem;
  font-family: 'Inter', sans-serif;
  font-style: normal;
  font-weight: 400;
  font-size: 1rem;
  line-height: 1.188rem;
  text-transform: uppercase;
  color: #FFFFFF;
}
.Label2 {
  position: absolute;
  width: auto;
  height: auto;
  left: 3.75rem;
  top: 9.125rem;
  font-family: 'Inter';
  font-style: normal;
  font-weight: 600;
  font-size: 4rem;
  line-height: 4.813rem;
  color: #59ACFF;
}

.Page1 {
  .input {
    position: absolute;
    width: 16.625rem;
    height: 1.688rem;
    left: 3.75rem;
    top: 14.875rem;
    background: linear-gradient(90deg, rgba(0, 0, 0, 0.5) 0.38%, rgba(0, 0, 0, 0.18) 100%);
    .InputSVG {
      position: absolute;
      margin: auto auto;
      top: 0;
      bottom: 0;
      left: .4rem;
    }
    > input {
      position: absolute;
      right: 0;
      width: 89%;
      height: 100%;
      background: none;
      border: none;
      outline: none;
      color: #ffffff9c;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: .938rem;
      line-height: 1.125rem;
      ::placeholder {
        color: #ffffff9c;
      }
    }
  }
  .Total {
    @include Border();
    left: 3.75rem;
  }
  .InGarage {
    @include Border();
    width: 8.125rem;
    left: 10.625rem;
  }
  .List {
    position: absolute;
    height: 43rem;
    width: 15.3rem;
    left: 3.6rem;
    top: 20rem;
    overflow-y: scroll;
    overflow-x: hidden;
    &::-webkit-scrollbar {
      display: none;
    }

    .Vehicle {
      position: relative;
      width: 15rem;
      height: 9.25rem;
      left: .15rem;
      top: -.2rem;
      margin-top: 1.25rem;
      background: #00000060; // ! Test
      border-radius: .688rem;
      overflow: hidden;
      transition: .47s all;
      z-index: 5;
      &:hover {
        transform: scale(.92);
        .Select {
          opacity: 1;
          height: .55rem;
        }
      }
      .Select {
        position: absolute;
        width: 100%;
        opacity: 0;
        height: 0;
        bottom: 0;
        background: #59ACFF;
        border-radius: 0 0 .688rem .688rem;
        transition: .47s all;
      }
      .VehName {
        position: absolute;
        width: auto;
        height: auto;
        margin: auto auto;
        left: 0;
        right: 0;
        top: .65rem;
        > p {
          text-align: center;
          font-family: 'Inter';
          font-style: normal;
          font-weight: 500;
          font-size: .875rem;
          margin: 0;
          line-height: normal;
          text-transform: uppercase;
          color: #59ACFF;
        }
      }
      .VehClass {
        position: absolute;
        width: auto;
        height: auto;
        margin: auto auto;
        left: 0;
        right: 0;
        top: 1.7rem;
        > p {
          text-align: center;
          font-family: 'Inter';
          font-style: normal;
          font-weight: 400;
          font-size: 10px;
          margin: 0;
          line-height: normal;
          text-transform: uppercase;
          color: #FFFFFF;
        }
      }
      .ImgBackground {
        position: absolute;
        margin: auto auto;
        left: 0;
        right: 0;
        bottom: -.4rem;
        z-index: -1;
      }
      .Image {
        position: absolute;
        width: 10.063rem;
        height: 5.313rem;
        margin: auto auto;
        left: 0;
        right: 0;
        bottom: 1rem;
      //  background: url('https://cdn.discordapp.com/attachments/969012565088809010/992397661686743051/image.png');
        background-image: url('https://cdn.discordapp.com/attachments/969012565088809010/992397661686743051/image.png');
        background-size: contain;
        background-position: center center;
        background-repeat: no-repeat;
      }
    }

  }
}


.Page2 {
  .BackPage {
    position: absolute;
    width: 5.688rem;
    height: 1.688rem;
    left: 3.75rem;
    top: 17.188rem;
    background: #00000080;
    > p {
      position: absolute;
      width: auto;
      height: auto;
      margin: auto auto;
      right: .32rem;
      bottom: 0;
      top: 0;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: .875rem;
      line-height: 1.8rem;
      text-transform: uppercase;
      color: #FFFFFF;
    }
  }

  .SelectBox {
    position: absolute;
    width: 15rem;
    height: 30.25rem;
    left: 3.75rem;
    top: 21rem;
    background: #00000080;
    border-radius: .688rem;
    .SelectButton {
      position: absolute;
      width: 10.438rem;
      height: 1.75rem;
      margin: auto auto;
      left: 0;
      right: 0;
      bottom: .9rem;
      background: #4b4b4b4d;
      border-radius: .563rem;
      
      text-align: center;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: .688rem;
      line-height: 1.8rem;
      color: #FFFFFF;
      transition: .47s all;
      &:hover {
        transform: scale(.95);
        background: #59ACFF;
      }
    }
    .Price {
      box-sizing: border-box;
      text-align: center;
      position: absolute;
      width: 100%;
      height: auto;
      margin: auto auto;
      left: 0;
      right: 0;
      bottom: 6.2rem;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: 1rem;
      line-height: 1.188rem;
      color: #ffffff;
      > span { color: #59ACFF; }
    }
    .Plate {
      box-sizing: border-box;
      position: absolute;
      width: 5rem;
      height: 2rem;
      margin: auto auto;
      left: 0;
      right: 0;
      bottom: 3.8rem;
      background: rgba(0, 0, 0, .7);
      border: .063rem solid #E0B82A;
      border-radius: .188rem;
      .Header {
        position: absolute;
        width: auto;
        margin: auto auto;
        right: 0;
        left: 0;
        top: .15rem;
        text-align: center;
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-size: .313rem;
        line-height: .375rem;
        color: #E0B82A;
        opacity: 0.3;
        border: .063rem solid #000000;
      }
      .Text {
        position: absolute;
        width: auto;
        height: auto;
        margin: auto auto;
        right: 0;
        left: 0;
        bottom: .1rem;
        text-align: center;
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-size: .813rem;
        line-height: 1.188rem;
        color: #E0B82A;
      }
    }
  }

  .Vehicle {
    position: absolute;
    width: 15rem;
    height: 11.7rem;
    left: 0;
    top: 0;
    border-radius: .688rem;
    .VehName {
      position: absolute;
      width: auto;
      height: auto;
      margin: auto auto;
      left: 0;
      right: 0;
      top: .8rem;
      > p {
        text-align: center;
        font-family: 'Inter';
        font-style: normal;
        font-weight: 500;
        font-size: 1rem;
        margin: 0;
        line-height: normal;
        text-transform: uppercase;
        color: #59ACFF;
      }
    }
    .VehClass {
      position: absolute;
      width: auto;
      height: auto;
      margin: auto auto;
      left: 0;
      right: 0;
      top: 2.05rem;
      > p {
        text-align: center;
        font-family: 'Inter';
        font-style: normal;
        font-weight: 400;
        font-size: .75rem;
        margin: 0;
        line-height: normal;
        text-transform: uppercase;
        color: #FFFFFF;
      }
    }
    .ImgBackground {
      position: absolute;
      margin: auto auto;
      left: 0;
      right: 0;
      bottom: -.4rem;
    }
    .Image {
      position: absolute;
      width: 10.063rem;
      height: 5.313rem;
      margin: auto auto;
      left: 0;
      right: 0;
      bottom: 1rem;
    //  background: url('https://cdn.discordapp.com/attachments/969012565088809010/992397661686743051/image.png');
      background-size: contain;
      background-position: center center;
      background-repeat: no-repeat;
      z-index: 1;
    }
  }

  .VehInfo {
    position: relative;
    height: 1.5rem;
    width: 94%;
    margin: auto auto;
    left: 0;
    right: 0;
    top: 12.5rem;
    margin-top: .7rem;
    .Image {
      position: absolute;
      bottom: .25rem;
      left: .25rem;
    }
    .Header {
      position: absolute;
      width: auto;
      height: auto;
      left: 1.3rem;
      top: .2rem;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: .625rem;
      line-height: .75rem;
      color: #FFFFFF;
    }
    .Label {
      position: absolute;
      width: auto;
      height: auto;
      left: 9.8rem;
      top: .56rem;
      font-family: 'Inter';
      font-style: normal;
      font-weight: 400;
      font-size: .875rem;
      line-height: 1.063rem;
      color: #FFFFFF;
    }
    .LoadBarSVG {
      position: absolute;
      bottom: .15rem;
      left: 1.25rem;
    }
  }
}



































.GarageName {
  position: absolute;
  left: 2.35rem;
  bottom: 8.3rem;
}

.Info {
  position: absolute;
  height: 12rem;
  width: 2rem;
  left: 2rem;
  top: 11.5rem;
  background-color: rgba(102, 51, 153, 0.603);
}


/* INFORMATION ABOUT YOUR GARAGE */
/*
  position: absolute;
  width: 255px;
  height: 23px;
  left: 65px;
  top: 442px;
  font-family: 'Rajdhani';
  font-style: normal;
  font-weight: 500;
  font-size: 18px;
  line-height: 23px;
  color: #FFFFFF;
  transform: rotate(-90deg);
*/

.Location {
  position: absolute;
  height: 3rem;
  width: auto;
  bottom: 17.25rem;
  left: 2.8rem;
  .POSVG {
    position: absolute;
    margin: auto auto;
    top: 0;
    bottom: 0;
  }
  .Text1 {
    text-align: left;
    font-family: 'Proxima_Nova_Condensed_Bold_Italic';
    font-weight: 700;
    font-size: 1.438rem;
    text-indent: 1.78rem;
    line-height: 1.7rem;
    color: #FFD600;
    transition: .32s all;
  }
  .Text2 {
    font-family: 'Montserrat';
    font-style: normal;
    font-weight: 500;
    font-size: 1.125rem;
    line-height: 1.37rem;
    text-indent: 1.78rem;
    color: #FFFFFF;
    transition: .32s all;
  }
}


.Maps {
  position: absolute;
  height: 12rem;
  width: 19.35rem;
  left: 1.7rem;
  bottom: 4.2rem;
  background: #00000021;
  border: .15rem solid #FFD600;
  border-radius: .5rem; //.5rem
}