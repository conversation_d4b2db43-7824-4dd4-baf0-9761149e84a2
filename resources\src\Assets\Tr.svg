<svg width="1920" height="1080" viewBox="0 0 1920 1080" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_172_6370)">
<rect width="1122" height="1080" fill="url(#paint0_linear_172_6370)"/>
<rect width="1122" height="1080" fill="url(#paint1_linear_172_6370)"/>
<g opacity="0.5" filter="url(#filter0_f_172_6370)">
<rect x="-426" y="865.633" width="651.634" height="545.63" rx="272.815" transform="rotate(-90 -426 865.633)" fill="url(#paint2_linear_172_6370)"/>
</g>
</g>
<defs>
<filter id="filter0_f_172_6370" x="-926" y="-286" width="1545.63" height="1651.63" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="250" result="effect1_foregroundBlur_172_6370"/>
</filter>
<linearGradient id="paint0_linear_172_6370" x1="0" y1="540" x2="1122" y2="540" gradientUnits="userSpaceOnUse">
<stop offset="0.21875"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint1_linear_172_6370" x1="0" y1="540" x2="1122" y2="540" gradientUnits="userSpaceOnUse">
<stop offset="0.21875"/>
<stop offset="1" stop-opacity="0"/>
</linearGradient>
<linearGradient id="paint2_linear_172_6370" x1="-426.278" y1="1138.81" x2="225.356" y2="1138.81" gradientUnits="userSpaceOnUse">
<stop stop-color="#406CDE"/>
</linearGradient>
<clipPath id="clip0_172_6370">
<rect width="1920" height="1080" fill="white"/>
</clipPath>
</defs>
</svg>
